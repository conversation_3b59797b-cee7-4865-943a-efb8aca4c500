<template>
  <div class="modern-header-content">
    <!-- Logo和品牌区域 -->
    <div class="header-brand">
      <RouterLink to="/" class="brand-link">
        <div class="brand-logo">
          <BaseIcon name="app-logo" size="28" />
        </div>
        <div class="brand-text" :class="{ 'hidden-mobile': compactMode }">
          <h1 class="brand-title">软件发布系统</h1>
          <span class="brand-subtitle">Software Release Platform</span>
        </div>
      </RouterLink>
    </div>

    <!-- 主导航菜单 -->
    <nav class="header-nav" :class="{ 'nav-open': mobileNavOpen }">
      <div class="nav-backdrop" @click="closeMobileNav" />
      <div class="nav-content">
        <div class="nav-header visible-mobile">
          <div class="nav-brand">
            <BaseIcon name="app-logo" size="24" />
            <span class="nav-title">导航菜单</span>
          </div>
          <button class="nav-close" @click="closeMobileNav" aria-label="关闭菜单">
            <BaseIcon name="x" size="20" />
          </button>
        </div>
        
        <ul class="nav-list">
          <li class="nav-item">
            <RouterLink to="/" class="nav-link" @click="closeMobileNav">
              <BaseIcon name="home" size="18" />
              <span>首页</span>
            </RouterLink>
          </li>
          <li class="nav-item">
            <RouterLink to="/projects" class="nav-link" @click="closeMobileNav">
              <BaseIcon name="folder" size="18" />
              <span>项目</span>
            </RouterLink>
          </li>
          <li class="nav-item">
            <RouterLink to="/search" class="nav-link" @click="closeMobileNav">
              <BaseIcon name="search" size="18" />
              <span>搜索</span>
            </RouterLink>
          </li>
        </ul>

        <!-- 移动端用户操作 -->
        <div class="nav-user-mobile visible-mobile">
          <div class="theme-switcher-mobile">
            <label class="theme-label">主题</label>
            <select 
              :value="app.theme" 
              @change="handleThemeChange"
              class="theme-select"
            >
              <option value="light">浅色</option>
              <option value="dark">深色</option>
              <option value="system">跟随系统</option>
            </select>
          </div>
          
          <div class="user-actions-mobile">
            <template v-if="currentAuth">
              <div class="user-info-mobile">
                <Avatar :user="currentUser" size="small" />
                <div class="user-details">
                  <span class="user-name">{{ currentUser?.name || currentUser?.username }}</span>
                  <span class="user-type">{{ currentUser?.userType === 'admin' ? '管理员' : '开发者' }}</span>
                </div>
              </div>
              <div class="user-menu-mobile">
                <RouterLink 
                  v-if="adminAuth.isAuthenticated" 
                  to="/admin" 
                  class="user-link-mobile"
                  @click="closeMobileNav"
                >
                  <BaseIcon name="shield" size="16" />
                  <span>管理后台</span>
                </RouterLink>
                <RouterLink 
                  v-if="developerAuth.isAuthenticated" 
                  to="/dev" 
                  class="user-link-mobile"
                  @click="closeMobileNav"
                >
                  <BaseIcon name="user" size="16" />
                  <span>开发者中心</span>
                </RouterLink>
                <button @click="handleLogout" class="logout-btn-mobile">
                  <BaseIcon name="log-out" size="16" />
                  <span>退出登录</span>
                </button>
              </div>
            </template>
            <template v-else>
              <div class="login-buttons-mobile">
                <RouterLink to="/dev/login" class="login-btn-mobile developer" @click="closeMobileNav">
                  <BaseIcon name="user" size="16" />
                  <span>开发者登录</span>
                </RouterLink>
                <RouterLink to="/admin/login" class="login-btn-mobile admin" @click="closeMobileNav">
                  <BaseIcon name="shield" size="16" />
                  <span>管理员登录</span>
                </RouterLink>
              </div>
            </template>
          </div>
        </div>
      </div>
    </nav>

    <!-- 桌面端用户区域 -->
    <div class="header-user hidden-mobile">
      <!-- 主题切换 -->
      <div class="theme-switcher">
        <select 
          :value="app.theme" 
          @change="handleThemeChange"
          class="theme-select"
          aria-label="切换主题"
        >
          <option value="light">浅色</option>
          <option value="dark">深色</option>
          <option value="system">跟随系统</option>
        </select>
      </div>

      <!-- 用户状态 -->
      <div class="user-actions">
        <template v-if="currentAuth">
          <!-- 已登录用户 -->
          <div class="user-info">
            <Avatar :user="currentUser" size="small" />
            <span class="user-name">{{ currentUser?.name || currentUser?.username }}</span>
            <span class="user-type-badge">{{ currentUser?.userType === 'admin' ? '管理员' : '开发者' }}</span>
          </div>
          <div class="user-menu">
            <RouterLink 
              v-if="adminAuth.isAuthenticated" 
              to="/admin" 
              class="user-link"
            >
              管理后台
            </RouterLink>
            <RouterLink 
              v-if="developerAuth.isAuthenticated" 
              to="/dev" 
              class="user-link"
            >
              开发者中心
            </RouterLink>
            <button @click="handleLogout" class="logout-btn">
              退出登录
            </button>
          </div>
        </template>
        <template v-else>
          <!-- 未登录用户 -->
          <div class="login-buttons">
            <RouterLink to="/dev/login" class="login-btn developer">
              <BaseIcon name="user" size="16" />
              <span>开发者</span>
            </RouterLink>
            <RouterLink to="/admin/login" class="login-btn admin">
              <BaseIcon name="shield" size="16" />
              <span>管理员</span>
            </RouterLink>
          </div>
        </template>
      </div>
    </div>

    <!-- 移动端菜单按钮 -->
    <button 
      class="mobile-nav-toggle visible-mobile"
      @click="toggleMobileNav"
      :aria-expanded="mobileNavOpen"
      aria-label="切换导航菜单"
    >
      <span class="hamburger" :class="{ 'active': mobileNavOpen }"></span>
    </button>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { RouterLink, useRouter } from 'vue-router'
import { useDeveloperAuthStore } from '@/stores/developerAuth'
import { useAdminAuthStore } from '@/stores/adminAuth'
import { useAppStore } from '@/stores/app'
import BaseIcon from '@base/atoms/BaseIcon.vue'
import Avatar from '@base/atoms/Avatar.vue'

interface Props {
  compactMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  compactMode: false
})

const router = useRouter()
const developerAuth = useDeveloperAuthStore()
const adminAuth = useAdminAuthStore()
const app = useAppStore()

// 计算当前认证状态
const currentAuth = computed(() => {
  if (developerAuth.isAuthenticated) return developerAuth
  if (adminAuth.isAuthenticated) return adminAuth
  return null
})

const currentUser = computed(() => currentAuth.value?.user || null)

// 移动端导航状态
const mobileNavOpen = ref(false)

// 切换移动端导航
const toggleMobileNav = () => {
  mobileNavOpen.value = !mobileNavOpen.value
  // 防止背景滚动
  document.body.style.overflow = mobileNavOpen.value ? 'hidden' : ''
}

// 关闭移动端导航
const closeMobileNav = () => {
  mobileNavOpen.value = false
  document.body.style.overflow = ''
}

// 处理主题切换
const handleThemeChange = (event: Event) => {
  const target = event.target as HTMLSelectElement
  app.setTheme(target.value as any)
}

// 处理退出登录
const handleLogout = () => {
  if (developerAuth.isAuthenticated) {
    developerAuth.logout()
  }
  if (adminAuth.isAuthenticated) {
    adminAuth.logout()
  }
  closeMobileNav()
  router.push('/')
}

// 监听路由变化，自动关闭移动端导航
router.beforeEach(() => {
  closeMobileNav()
})
</script>

<style scoped>
.modern-header-content {
  display: grid;
  grid-template-columns: auto 1fr auto auto;
  align-items: center;
  gap: var(--spacing-4);
  min-height: 72px;
  position: relative;
}

/* Logo品牌区域 */
.header-brand {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  text-decoration: none;
  color: inherit;
  transition: var(--transition-transform);
}

.brand-link:hover {
  transform: scale(1.02);
}

.brand-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: var(--gradient-primary);
  border-radius: var(--radius-xl);
  color: white;
  box-shadow: var(--shadow-sm);
}

.brand-text {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-0_5);
}

.brand-title {
  font-family: var(--font-family-display);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin: 0;
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
  letter-spacing: -0.02em;
}

.brand-subtitle {
  font-size: var(--font-size-2xs);
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* 主导航样式 */
.header-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.nav-content {
  position: relative;
}

.nav-list {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  list-style: none;
  margin: 0;
  padding: var(--spacing-1);
  background: var(--glass-bg);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  box-shadow: var(--shadow-sm);
}

.nav-item {
  display: flex;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2_5) var(--spacing-4);
  border-radius: var(--radius-xl);
  text-decoration: none;
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  transition: var(--transition-all);
  position: relative;
  white-space: nowrap;
}

.nav-link:hover {
  background: var(--bg-secondary);
  color: var(--color-text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.nav-link.router-link-active {
  background: var(--color-primary);
  color: white;
  box-shadow: var(--shadow-sm);
}

.nav-link.router-link-active:hover {
  background: var(--color-primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 桌面端用户区域 */
.header-user {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex-shrink: 0;
}

.theme-switcher {
  display: flex;
  align-items: center;
}

.theme-select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1.5px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-colors);
}

.theme-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: var(--focus-ring);
}

.theme-select:hover {
  border-color: var(--border-secondary);
}

.user-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-xl);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

.user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  line-height: 1;
}

.user-type-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-2);
  background: var(--color-primary-100);
  color: var(--color-primary-700);
  border-radius: var(--radius-full);
  font-size: var(--font-size-2xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.03em;
}

.login-buttons {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.user-link {
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-all);
  border: 1px solid var(--border-primary);
}

.user-link:hover {
  background: var(--bg-secondary);
  color: var(--color-text-primary);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.login-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2_5) var(--spacing-4);
  border-radius: var(--radius-xl);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  transition: var(--transition-all);
  border: none;
  cursor: pointer;
  box-shadow: var(--shadow-sm);
}

.login-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.login-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.login-btn.developer {
  background: var(--gradient-primary);
  color: white;
}

.login-btn.developer:hover {
  background: var(--color-primary-600);
}

.login-btn.admin {
  background: var(--color-warning);
  color: white;
}

.login-btn.admin:hover {
  background: var(--color-warning-600);
}

.logout-btn {
  padding: var(--spacing-2) var(--spacing-3);
  background: transparent;
  border: 1.5px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-all);
}

.logout-btn:hover {
  background: var(--bg-secondary);
  color: var(--color-text-primary);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.logout-btn:focus {
  outline: none;
  box-shadow: var(--focus-ring);
}
</style>
