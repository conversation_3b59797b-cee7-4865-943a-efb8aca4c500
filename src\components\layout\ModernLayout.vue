<template>
  <div class="modern-layout" :class="layoutClasses">
    <!-- 现代化头部 -->
    <header class="modern-header" :class="headerClasses">
      <div class="header-container" :class="containerClass">
        <slot name="header">
          <ModernHeader />
        </slot>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="modern-main" :class="mainClasses">
      <div class="main-container" :class="containerClass">
        <!-- 面包屑导航 -->
        <nav v-if="showBreadcrumb" class="breadcrumb-nav">
          <slot name="breadcrumb">
            <ModernBreadcrumb />
          </slot>
        </nav>

        <!-- 页面内容 -->
        <div class="content-wrapper" :class="contentClasses">
          <!-- 侧边栏 -->
          <aside v-if="hasSidebar" class="content-sidebar" :class="sidebarClasses">
            <slot name="sidebar" />
          </aside>

          <!-- 主内容 -->
          <section class="content-main" :class="contentMainClasses">
            <slot />
          </section>

          <!-- 右侧栏 */
          <aside v-if="hasRightSidebar" class="content-right-sidebar" :class="rightSidebarClasses">
            <slot name="right-sidebar" />
          </aside>
        </div>
      </div>
    </main>

    <!-- 现代化底部 -->
    <footer class="modern-footer" :class="footerClasses">
      <div class="footer-container" :class="containerClass">
        <slot name="footer">
          <ModernFooter />
        </slot>
      </div>
    </footer>

    <!-- 浮动操作按钮 -->
    <div v-if="hasFab" class="fab-container">
      <slot name="fab" />
    </div>

    <!-- 全局加载状态 */
    <Teleport to="body">
      <div v-if="isLoading" class="global-loading">
        <div class="loading-backdrop" />
        <div class="loading-content">
          <div class="loading-spinner" />
          <p class="loading-text">{{ loadingText }}</p>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { computed, useSlots } from 'vue'
import ModernHeader from './ModernHeader.vue'
import ModernFooter from './ModernFooter.vue'
import ModernBreadcrumb from './ModernBreadcrumb.vue'

interface Props {
  // 布局配置
  layout?: 'default' | 'sidebar' | 'wide' | 'narrow' | 'dashboard'
  container?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
  
  // 头部配置
  headerFixed?: boolean
  headerTransparent?: boolean
  headerCompact?: boolean
  
  // 内容配置
  contentPadding?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  contentSpacing?: 'tight' | 'normal' | 'relaxed'
  
  // 侧边栏配置
  sidebarWidth?: 'sm' | 'md' | 'lg'
  sidebarPosition?: 'left' | 'right'
  sidebarCollapsible?: boolean
  
  // 状态
  showBreadcrumb?: boolean
  isLoading?: boolean
  loadingText?: string
}

const props = withDefaults(defineProps<Props>(), {
  layout: 'default',
  container: 'xl',
  headerFixed: true,
  headerTransparent: false,
  headerCompact: false,
  contentPadding: 'md',
  contentSpacing: 'normal',
  sidebarWidth: 'md',
  sidebarPosition: 'left',
  sidebarCollapsible: false,
  showBreadcrumb: false,
  isLoading: false,
  loadingText: '加载中...'
})

const slots = useSlots()

// 计算属性
const hasSidebar = computed(() => !!slots.sidebar)
const hasRightSidebar = computed(() => !!slots['right-sidebar'])
const hasFab = computed(() => !!slots.fab)

const layoutClasses = computed(() => [
  `layout-${props.layout}`,
  {
    'has-sidebar': hasSidebar.value,
    'has-right-sidebar': hasRightSidebar.value,
    'sidebar-collapsible': props.sidebarCollapsible,
    'is-loading': props.isLoading
  }
])

const containerClass = computed(() => `container-${props.container}`)

const headerClasses = computed(() => [
  {
    'header-fixed': props.headerFixed,
    'header-transparent': props.headerTransparent,
    'header-compact': props.headerCompact
  }
])

const mainClasses = computed(() => [
  {
    'main-with-fixed-header': props.headerFixed
  }
])

const contentClasses = computed(() => [
  `content-padding-${props.contentPadding}`,
  `content-spacing-${props.contentSpacing}`,
  {
    'content-with-sidebar': hasSidebar.value,
    'content-with-right-sidebar': hasRightSidebar.value
  }
])

const contentMainClasses = computed(() => [
  'container-query'
])

const sidebarClasses = computed(() => [
  `sidebar-${props.sidebarWidth}`,
  `sidebar-${props.sidebarPosition}`,
  'container-query'
])

const rightSidebarClasses = computed(() => [
  `sidebar-${props.sidebarWidth}`,
  'container-query'
])

const footerClasses = computed(() => [
  // 可以根据需要添加footer相关的类
])
</script>

<style scoped>
/* 现代化布局系统 */
.modern-layout {
  min-height: var(--vh-dynamic, 100vh);
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  color: var(--color-text-primary);
  font-family: var(--font-family-text);
  line-height: var(--line-height-normal);
}

/* 头部样式 */
.modern-header {
  position: relative;
  z-index: var(--z-sticky);
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  transition: var(--transition-all);
}

.modern-header.header-fixed {
  position: sticky;
  top: 0;
}

.modern-header.header-transparent {
  background: var(--glass-bg);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border-bottom-color: var(--glass-border);
}

.modern-header.header-compact .header-container {
  padding-top: var(--spacing-2);
  padding-bottom: var(--spacing-2);
}

.header-container {
  padding-top: var(--spacing-4);
  padding-bottom: var(--spacing-4);
}

/* 主要内容区域 */
.modern-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许内容收缩 */
}

.modern-main.main-with-fixed-header {
  /* 为固定头部预留空间 */
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 面包屑导航 */
.breadcrumb-nav {
  padding: var(--spacing-4) 0;
  border-bottom: 1px solid var(--border-primary);
  margin-bottom: var(--spacing-6);
}

/* 内容包装器 */
.content-wrapper {
  flex: 1;
  display: grid;
  gap: var(--spacing-6);
  min-height: 0;
  align-items: start;
}

/* 默认单列布局 */
.content-wrapper {
  grid-template-columns: 1fr;
  grid-template-areas: "main";
}

/* 带左侧边栏的布局 */
.content-wrapper.content-with-sidebar {
  grid-template-columns: auto 1fr;
  grid-template-areas: "sidebar main";
}

/* 带右侧边栏的布局 */
.content-wrapper.content-with-right-sidebar:not(.content-with-sidebar) {
  grid-template-columns: 1fr auto;
  grid-template-areas: "main right-sidebar";
}

/* 带双侧边栏的布局 */
.content-wrapper.content-with-sidebar.content-with-right-sidebar {
  grid-template-columns: auto 1fr auto;
  grid-template-areas: "sidebar main right-sidebar";
}

/* 内容区域 */
.content-main {
  grid-area: main;
  min-width: 0; /* 防止内容溢出 */
}

.content-sidebar {
  grid-area: sidebar;
  min-width: 0;
}

.content-right-sidebar {
  grid-area: right-sidebar;
  min-width: 0;
}

/* 侧边栏宽度 */
.sidebar-sm { width: 240px; }
.sidebar-md { width: 280px; }
.sidebar-lg { width: 320px; }

/* 内容间距 */
.content-padding-none { padding: 0; }
.content-padding-sm { padding: var(--spacing-4) 0; }
.content-padding-md { padding: var(--spacing-6) 0; }
.content-padding-lg { padding: var(--spacing-8) 0; }
.content-padding-xl { padding: var(--spacing-12) 0; }

.content-spacing-tight { gap: var(--spacing-4); }
.content-spacing-normal { gap: var(--spacing-6); }
.content-spacing-relaxed { gap: var(--spacing-8); }

/* 底部样式 */
.modern-footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  margin-top: auto;
}

.footer-container {
  padding: var(--spacing-8) 0;
}

/* 浮动操作按钮 */
.fab-container {
  position: fixed;
  bottom: var(--spacing-6);
  right: var(--spacing-6);
  z-index: var(--z-docked);
}

/* 全局加载状态 */
.global-loading {
  position: fixed;
  inset: 0;
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-backdrop {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.loading-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-8);
  background: var(--glass-bg-strong);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-xl);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-primary-200);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>
